name : minimal 

on:
  push:
    branches:  [master]
  pull_request:
    branches:  [master]

jobs:
  macos-artifacts:
    needs: [Export_Github_Repositories]
    strategy:
      fail-fast: false
    runs-on: macos-latest
    if : ${{ github.event_name == 'api' && 'cron' || 'push' || startsWith(github.ref, 'refs/tags/v')}}
    steps:
      - name: Checkout repository
        uses: actions/checkout@v2
      - name: Setup | Rust
        uses: ATiltedTree/setup-rust@v1
        with:
          rust-version: stable
      - name:  release artifact
        run: |
           source ci/rust-version.sh
           brew install coreutils
           export PATH="/usr/local/opt/coreutils/libexec/gnubin:$PATH"
           greadlink -f .
           source ci/env.sh
           rustup set profile default
           ci/publish-tarball.sh
        shell: bash
        
      - name: Cache modules
        uses: actions/cache@master
        id: yarn-cache
        with:
          path: node_modules
          key: ${{ runner.os }}-yarn-${{ hashFiles('**/yarn.lock') }}
          restore-keys: ${{ runner.os }}-yarn-
          

# - To stop from uploading on the production
#      - uses: ochanje210/simple-s3-upload-action@master
#        with:
#          AWS_ACCESS_KEY_ID: ${{ secrets.AWS_KEY_ID }}
#          AWS_SECRET_ACCESS_KEY: ${{ secrets.AWS_SECRET_ACCESS_KEY}}
#          AWS_S3_BUCKET: ${{ secrets.AWS_S3_BUCKET }}
#          SOURCE_DIR: 'travis-s3-upload1'
#          DEST_DIR: 'giitsol'

#      - uses: ochanje210/simple-s3-upload-action@master
#        with:
#          AWS_ACCESS_KEY_ID: ${{ secrets.AWS_KEY_ID }}
#          AWS_SECRET_ACCESS_KEY: ${{ secrets.AWS_SECRET_ACCESS_KEY}}
#          AWS_S3_BUCKET: ${{ secrets.AWS_S3_BUCKET }}
#          SOURCE_DIR: './docs/'
#          DEST_DIR: 'giitsol'

  
  windows-artifact:
    needs: [Export_Github_Repositories]
    strategy:
      fail-fast: false 
    runs-on: windows-latest
    if : ${{ github.event_name == 'api' && 'cron' || 'push' || startsWith(github.ref, 'refs/tags/v')}}
    steps:
      - name: Checkout repository
        uses: actions/checkout@v2
      - name: Setup | Rust
        uses: ATiltedTree/setup-rust@v1
        with:
          rust-version: stable
  release-artifact:
    needs: windows-artifact
    runs-on: windows-latest
    if : ${{ github.event_name == 'api' && 'cron' || github.ref == 'refs/heads/master'}}
    steps:
      - name: release artifact 
        run: |
             git clone git://git.openssl.org/openssl.git
             cd openssl
             make
             make test
             make install
             openssl version
#            choco install openssl 
#            vcpkg integrate install
#            refreshenv

      - name: Checkout repository
        uses: actions/checkout@v2 
      - uses: actions/checkout@v2
      - run: choco install msys2
      - uses: actions/checkout@v2
      - run: |
           openssl version
           bash ci/rust-version.sh
           readlink -f .
           bash ci/env.sh
           rustup set profile default
           bash ci/publish-tarball.sh
        shell: bash
        
      - name: Cache modules
        uses: actions/cache@v1
        id: yarn-cache
        with:
          path: node_modules
          key: ${{ runner.os }}-yarn-${{ hashFiles('**/yarn.lock') }}
          restore-keys: ${{ runner.os }}-yarn-

# - To stop from uploading on the production      
#      - name: Config. aws cred
#        uses: aws-actions/configure-aws-credentials@v1
#        with:
#          aws_access_key_id: ${{ secrets.AWS_ACCESS_KEY_ID }}
#          aws_secret_access_key: ${{ secrets.AWS_SECRET_ACCESS_KEY }}
#          aws-region: us-east-2
#      - name: Deploy
#        uses: shallwefootball/s3-upload-action@master
#        with:
#            folder: build
#            aws_bucket: ${{ secrets.AWS_S3_BUCKET }}
#            aws_key_id: ${{ secrets.AWS_ACCESS_KEY_ID }}
#            aws_secret_access_key: ${{ secrets.AWS_SECRET_ACCESS_KEY }}
#            destination_dir: /
#            bucket-region: us-east-2
#            delete-removed: true
#            no-cache: true
#            private: true
  
#  Docs:
#    needs: [windows-artifact,release-artifact]
#    runs-on: ubuntu-latest
#    env: 
#       GITHUB_TOKEN: ${{secrets.PAT_NEW}} 
#       GITHUB_EVENT_BEFORE: ${{ github.event.before }}
#       GITHUB_EVENT_AFTER: ${{ github.event.after }}
#       COMMIT_RANGE: ${{ github.event.before}}...${{ github.event.after}}
#    steps:
#      - name: Checkout repo
#        uses: actions/checkout@v2
#        with:
#          fetch-depth: 2
#      - name: docs
#        if: ${{github.event_name == 'pull_request' || startsWith(github.ref, 'refs/tags/v')}}
#        run: |
#             touch .env
#             echo "COMMIT_RANGE=($COMMIT_RANGE)" > .env
#             source ci/env.sh
#             .travis/channel_restriction.sh edge beta || exit 0
#             .travis/affects.sh docs/ .travis || exit 0
#             cd docs/
#             source .travis/before_install.sh
#             source .travis/script.sh
#      - name: setup-node
#        uses: actions/checkout@v2
#      - name: setup-node
#        uses: actions/setup-node@v2
#        with:
#          node-version: 'lts/*'
#      - name: Cache
#        uses: actions/cache@v1
#        with:
#          path: ~/.npm
#          key: ${{ runner.OS }}-npm-cache-${{ hashFiles('**/package-lock.json') }}
#          restore-keys: |
#            ${{ runner.OS }}-npm-cache-2  

#  auto_bump:
#     needs: [windows-artifact,release-artifact,Docs]
#     runs-on: ubuntu-latest
#     steps:
#       - name : checkout repo
#         uses: actions/checkout@v2
#         with:
#          fetch-depth: '0'
#       - name: Bump version and push tag
#         uses: anothrNick/github-tag-action@1.26.0
#         env:
#           GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
#           WITH_V: true
#           DEFAULT_BUMP: patch
