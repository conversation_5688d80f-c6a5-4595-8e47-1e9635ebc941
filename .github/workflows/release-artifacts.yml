name: release-artifacts

on:
  workflow_call:
    inputs:
      commit:
        required: false
        type: string
    secrets:
      AWS_ACCESS_KEY_ID:
        required: true
      AWS_SECRET_ACCESS_KEY:
        required: true
      AWS_S3_BUCKET:
        required: true

jobs:
  windows-build:
    runs-on: windows-2022
    outputs:
      tag: ${{ steps.build.outputs.tag }}
      channel: ${{ steps.build.outputs.channel }}
    steps:
      - name: Checkout
        uses: actions/checkout@v3
        with:
          ref: master
          fetch-depth: 0

      - name: Setup Rust
        shell: bash
        run: |
          source ci/rust-version.sh stable

      # copy the newest version env.sh before switching version.
      - name: Copy Env Script
        shell: bash
        run: |
          cp -r ci/env.sh /tmp/env.sh

      - name: Switch Version
        if: ${{ inputs.commit }}
        run: |
          git checkout ${{ inputs.commit }}

      - name: Build
        id: build
        shell: bash
        run: |
          vcpkg install openssl:x64-windows-static-md
          vcpkg integrate install
          choco install protoc
          export PROTOC="C:\ProgramData\chocolatey\lib\protoc\tools\bin\protoc.exe"
          source /tmp/env.sh
          echo "tag=$CI_TAG" >> $GITHUB_OUTPUT
          eval "$(ci/channel-info.sh)"
          echo "channel=$CHANNEL" >> $GITHUB_OUTPUT
          ci/publish-tarball.sh

      - name: Prepare Upload Files
        if: ${{ steps.build.outputs.channel != '' || steps.build.outputs.tag != '' }}
        shell: bash
        run: |
          FOLDER_NAME=${{ steps.build.outputs.tag || steps.build.outputs.channel }}
          mkdir -p "github-action-s3-upload/$FOLDER_NAME"
          cp -v "solana-release-x86_64-pc-windows-msvc.tar.bz2" "github-action-s3-upload/$FOLDER_NAME/"
          cp -v "solana-release-x86_64-pc-windows-msvc.yml" "github-action-s3-upload/$FOLDER_NAME/"
          cp -v "solana-install-init-x86_64-pc-windows-msvc"* "github-action-s3-upload/$FOLDER_NAME"

      - name: Upload Artifacts
        if: ${{ steps.build.outputs.channel != '' || steps.build.outputs.tag != '' }}
        uses: actions/upload-artifact@v3
        with:
          name: windows-artifact
          path: github-action-s3-upload/

  windows-s3-upload:
    if: ${{ needs.windows-build.outputs.channel != '' || needs.windows-build.outputs.tag != '' }}
    needs: [windows-build]
    runs-on: ubuntu-20.04
    steps:
      - name: Download
        uses: actions/download-artifact@v3
        with:
          name: windows-artifact
          path: ./github-action-s3-upload

      - name: Upload
        uses: jakejarvis/s3-sync-action@master
        with:
          args: --acl public-read --follow-symlinks
        env:
          AWS_ACCESS_KEY_ID: ${{ secrets.AWS_ACCESS_KEY_ID }}
          AWS_SECRET_ACCESS_KEY: ${{ secrets.AWS_SECRET_ACCESS_KEY }}
          AWS_S3_BUCKET: ${{ secrets.AWS_S3_BUCKET }}
          AWS_REGION: "us-west-1"
          SOURCE_DIR: "github-action-s3-upload"

  windows-gh-release:
    if: ${{ needs.windows-build.outputs.tag != '' }}
    needs: [windows-build]
    runs-on: ubuntu-20.04
    steps:
      - name: Download
        uses: actions/download-artifact@v3
        with:
          name: windows-artifact
          path: ./github-action-s3-upload

      - name: Release
        uses: softprops/action-gh-release@v1
        with:
          tag_name: ${{ needs.windows-build.outputs.tag }}
          draft: true
          files: |
            github-action-s3-upload/${{ needs.windows-build.outputs.tag }}/*
