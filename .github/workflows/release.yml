name: Release

on:
  push:
    tags:
      - "*"

jobs:
  trigger-buildkite-pipeline:
    runs-on: ubuntu-latest
    steps:
      - name: Trigger a Buildkite Build
        uses: "buildkite/trigger-pipeline-action@v2.0.0"
        with:
          buildkite_api_access_token: ${{ secrets.TRIGGER_BK_BUILD_TOKEN }}
          pipeline: "solana-labs/solana-secondary"
          branch: "${{ github.ref_name }}"
          build_env_vars: '{"TRIGGERED_BUILDKITE_TAG": "${{ github.ref_name }}"}'
          commit: "HEAD"
          message: ":github: Triggered from a GitHub Action"

  draft-release:
    runs-on: ubuntu-latest
    steps:
      - name: Create Release
        uses: actions/github-script@v7
        with:
          github-token: ${{ secrets.GITHUB_TOKEN }}
          script: |
            github.rest.repos.createRelease({
              owner: context.repo.owner,
              repo: context.repo.repo,
              tag_name: '${{ github.ref_name }}',
              name: 'Release ${{ github.ref_name }}',
              body: '🚧',
              draft: false,
              prerelease: true
            })
